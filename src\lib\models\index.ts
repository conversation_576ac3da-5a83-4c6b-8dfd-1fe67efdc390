import fs from 'fs';
import path from 'path';
import { Sequelize, DataTypes } from 'sequelize';

const basename = path.basename(__filename);

// Define the database interface
interface Database {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
  sequelize: Sequelize;
  Sequelize: typeof Sequelize;
}

const { DATABASE_NAME, DATABASE_USER, DATABASE_PASS, DATABASE_HOST } = process.env;

const sequelize = new Sequelize(DATABASE_NAME!, DATABASE_USER!, DATABASE_PASS!, {
  host: DATABASE_HOST,
  dialect: 'postgres',
  port: parseInt(process.env.DATABASE_PORT || '5432', 10),
  pool: {
    max: 10,
    min: 2,
    acquire: 30000, // Max time in ms to get a connection before throwing error
    idle: 10000 // Connection closes after idle timeout
  }
});

const db: Database = {} as Database;

// Import models dynamically
fs
  .readdirSync(__dirname)
  .filter((file: string) => {
    return (
      file.indexOf('.') !== 0 &&
      file !== basename &&
      (file.slice(-3) === '.ts' || file.slice(-3) === '.js') &&
      file.indexOf('.test.') === -1 &&
      file !== 'index.ts' &&
      file !== 'index.js'
    );
  })
  .forEach((file: string) => {
    const modelPath = path.join(__dirname, file);
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const modelModule = require(modelPath);
    const model = modelModule.default ? modelModule.default(sequelize, DataTypes) : modelModule(sequelize, DataTypes);
    db[model.name] = model;
  });

Object.keys(db).forEach((modelName: string) => {
  if (db[modelName].associate) {
    db[modelName].associate(db);
  }
});

db.sequelize = sequelize;
db.Sequelize = Sequelize;

// Export individual models for easier access
export const models = db;
export { sequelize };
export default db;
